#!/bin/bash

# Define project directories
PROJECT_ROOT="$PWD"
FRONTEND_DIR="$PROJECT_ROOT/frontend"
ADMIN_DIR="$PROJECT_ROOT/admin"

# PM2 process names
ADMIN_PM2_NAME="admin-app"
FRONTEND_PM2_NAME="frontend-app"

# Function to check if a command was successful
check_success() {
    if [ $? -eq 0 ]; then
        echo "✅ $1 successful"
    else
        echo "❌ $1 failed"
        exit 1
    fi
}

# Navigate to project root
cd "$PROJECT_ROOT" || exit
echo "📂 Navigated to project root: $PROJECT_ROOT"

# Pull latest changes
echo "🔄 Pulling latest changes from git..."
git pull
check_success "Git pull"

# Build Backend (Payload CMS)
echo "🏗️ Building admin..."
cd "$ADMIN_DIR" || exit
pnpm install
check_success "Backend pnpm install"
pnpm build
check_success "Backend build"

# Update PM2 admin process
echo "🔄 Updating PM2 admin process..."
pm2 delete $ADMIN_PM2_NAME || true
pm2 start pnpm --name $ADMIN_PM2_NAME -- serve
check_success "Backend PM2 update"

export NODE_ENV="production"

# Build Frontend (Next.js)
echo "🏗️ Building frontend..."
cd "$FRONTEND_DIR" || exit
pnpm install
check_success "Frontend pnpm install"
pnpm build
check_success "Frontend build"

# Update PM2 frontend process
echo "🔄 Updating PM2 frontend process..."
pm2 delete $FRONTEND_PM2_NAME || true
pm2 start pnpm --name $FRONTEND_PM2_NAME -- start
check_success "Frontend PM2 update"

# Save PM2 process list
pm2 save
check_success "PM2 save"

# restart the nginx server
systemctl restart nginx
check_success "nginx restart"

echo "✨ Deployment completed successfully!"
