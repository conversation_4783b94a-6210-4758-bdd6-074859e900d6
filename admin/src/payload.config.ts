import path from "path";

import { payloadCloud } from "@payloadcms/plugin-cloud";
import { mongooseAdapter } from "@payloadcms/db-mongodb";
import { webpackBundler } from "@payloadcms/bundler-webpack";
import { slateEditor } from "@payloadcms/richtext-slate";
import { buildConfig } from "payload/config";

import Users from "./collections/Users";
import Slides from "./collections/Slides";
import Games from "./collections/Games";
import Blogs from "./collections/Blogs";
import JobListing from "./collections/JobListing";
import JobApplications from "./collections/JobApplications";
import Contact from "./collections/Contact";
import { Media } from "./collections/Media";

export default buildConfig({
  cors: [...process.env.PAYLOAD_PUBLIC_SITE_URL.split(",")],
  admin: {
    user: Users.slug,
    bundler: webpackBundler(),
  },
  editor: slateEditor({}),
  collections: [
    Users,
    Slides,
    Games,
    Blogs,
    JobListing,
    JobApplications,
    Contact,
    Media,
  ],
  typescript: {
    outputFile: path.resolve(__dirname, "payload-types.ts"),
  },
  plugins: [payloadCloud()],
  db: mongooseAdapter({
    url: process.env.DATABASE_URI,
  }),
});
