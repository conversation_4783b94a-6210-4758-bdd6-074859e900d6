import { CollectionConfig } from "payload/types";

const Blogs: CollectionConfig = {
  slug: "blogs",
  admin: {
    hideAPIURL: true,
  },
  access: {
    read: () => true,
  },
  fields: [
    {
      name: "title",
      type: "text",
      required: true,
    },
    {
      name: "slug",
      type: "text",
      required: true,
    },
    {
      name: "coverImage",
      type: "upload",
      relationTo: "media",
      required: true,
    },
    {
      name: "content",
      type: "richText",
      required: true,
    },
  ],
  endpoints: [
    {
      path: "/slug/:slug",
      method: "get",
      handler: async (req, res, next) => {
        const { slug } = req.params;
        const game = await req.payload.find({
          collection: "blogs",
          where: {
            slug: { equals: slug },
          },
        });

        if (game.docs.length > 0) {
          res.status(200).json(game.docs[0]);
        } else {
          res.status(404).json({ error: "Blog not found" });
        }
      },
    },
  ],
};

export default Blogs;
