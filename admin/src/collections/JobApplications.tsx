import { CollectionConfig } from "payload/types";
import path from "path";

const JobApplications: CollectionConfig = {
  slug: "jobapplications",
  admin: {
    hideAPIURL: true,
  },
  labels: {
    singular: "Job Application",
    plural: "Job Applications",
  },
  upload: {
    staticDir: path.resolve(__dirname, "../../../resumes"),
  },
  access: {
    create: () => true,
  },
  fields: [
    {
      name: "name",
      type: "text",
      required: true,
    },
    {
      name: "email",
      type: "email",
      required: true,
    },
    {
      name: "mobile",
      type: "text",
      required: true,
    },
    {
      name: "portfolio",
      type: "text",
      required: true,
    },
    {
      name: "job",
      type: "relationship",
      relationTo: "joblisting",
      required: true,
      hasMany: false,
      admin: {
        readOnly: true,
        position: "sidebar",
        allowCreate: false,
      },
    },
  ],
};

export default JobApplications;
