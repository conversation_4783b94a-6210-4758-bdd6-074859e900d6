import { CollectionConfig } from "payload/types";
import path from "path";

const Contact: CollectionConfig = {
  slug: "contact",
  upload: {
    staticDir: path.resolve(__dirname, "../../../contacts"),
  },
  access: {
    create: () => true,
  },
  fields: [
    {
      name: "name",
      type: "text",
      required: true,
    },
    {
      name: "email",
      type: "email",
      required: true,
    },
    {
      name: "mobile",
      type: "text",
      required: true,
    },
    {
      name: "topic",
      type: "text",
      required: true,
    },
    {
      name: "requirements",
      type: "text",
      required: true,
    },
  ],
  endpoints: [],
};

export default Contact;
