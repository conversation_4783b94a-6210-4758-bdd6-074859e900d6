import { CollectionConfig } from "payload/types";

const Slides: CollectionConfig = {
  slug: "slides",
  access: {
    read: () => true,
  },
  fields: [
    {
      name: "titleImage",
      type: "upload",
      relationTo: "media",
      required: true,
    },
    {
      name: "bgImage",
      type: "upload",
      relationTo: "media",
      required: true,
    },
    {
      name: "btnImage",
      type: "upload",
      relationTo: "media",
      required: true,
    },
    {
      name: "btnLink",
      type: "text",
      required: true,
    },
    {
      name: "desc",
      type: "textarea",
      required: true,
    },
  ],
};

export default Slides;
