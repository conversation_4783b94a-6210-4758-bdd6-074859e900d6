import { CollectionConfig } from "payload/types";

const JobListing: CollectionConfig = {
  slug: "joblisting",
  admin: {
    useAsTitle: "title",
    hideAPIURL: true,
  },
  labels: {
    plural: "Job Listings",
    singular: "Job Listing",
  },
  access: {
    read: () => true,
  },
  fields: [
    {
      name: "title",
      type: "text",
      required: true,
    },
    {
      name: "slug",
      type: "text",
      required: true,
    },
    {
      name: "desc",
      type: "text",
      required: true,
    },
    {
      name: "content",
      type: "richText",
      required: true,
    },
    {
      name: "jobType",
      type: "select",
      required: true,
      options: [
        {
          label: "Full Time",
          value: "Full Time",
        },
        {
          label: "Part Time",
          value: "Part Time",
        },
        {
          label: "Contract",
          value: "Contract",
        },
      ],
    },
  ],
  endpoints: [
    {
      path: "/slug/:slug",
      method: "get",
      handler: async (req, res, next) => {
        const { slug } = req.params;
        const data = await req.payload.find({
          collection: "joblisting",
          where: {
            slug: { equals: slug },
          },
        });

        if (data.docs.length > 0) {
          res.status(200).json(data.docs[0]);
        } else {
          res.status(404).json({ error: "Job not found" });
        }
      },
    },
  ],
};

export default JobListing;
