import { CollectionConfig } from "payload/types";

const Games: CollectionConfig = {
  slug: "games",
  admin: {
    hideAPIURL: true,
  },
  access: {
    read: () => true,
  },
  fields: [
    {
      name: "title",
      type: "text",
      required: true,
    },
    {
      name: "slug",
      type: "text",
      required: true,
    },
    {
      name: "tagline",
      type: "text",
      required: true,
    },
    {
      name: "coverImage",
      type: "upload",
      relationTo: "media",
      required: true,
    },
    {
      name: "genres",
      type: "text",
      required: true,
    },
    {
      name: "artstyles",
      type: "text",
      required: true,
      label: "Art Styles",
    },
    {
      name: "platforms",
      type: "text",
      required: true,
    },
    {
      name: "content",
      type: "richText",
      required: true,
    },
    {
      name: "links",
      type: "array",
      fields: [
        {
          name: "platform",
          type: "select",
          options: [
            {
              label: "Steam",
              value: "Steam",
            },
            {
              label: "Epic",
              value: "Epic",
            },
            {
              label: "App Store",
              value: "App Store",
            },
            {
              label: "Play Store",
              value: "Play Store",
            },
          ],
          required: true,
        },
        {
          name: "link",
          type: "text",
          required: true,
        },
      ],
    },
  ],
  endpoints: [
    {
      path: "/slug/:slug",
      method: "get",
      handler: async (req, res, next) => {
        const { slug } = req.params;
        const game = await req.payload.find({
          collection: "games",
          where: {
            slug: { equals: slug },
          },
        });

        if (game.docs.length > 0) {
          res.status(200).json(game.docs[0]);
        } else {
          res.status(404).json({ error: "Game not found" });
        }
      },
    },
  ],
};

export default Games;
