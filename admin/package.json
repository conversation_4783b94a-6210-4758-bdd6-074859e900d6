{"name": "admin", "description": "A blank template to get started with Payload", "version": "1.0.0", "main": "dist/server.js", "license": "MIT", "scripts": {"dev": "cross-env PAYLOAD_CONFIG_PATH=src/payload.config.ts nodemon", "build:payload": "cross-env PAYLOAD_CONFIG_PATH=src/payload.config.ts payload build", "build:server": "tsc", "build": "pnpm copyfiles && pnpm build:payload && pnpm build:server", "serve": "cross-env PAYLOAD_CONFIG_PATH=dist/payload.config.js NODE_ENV=production node dist/server.js", "copyfiles": "copyfiles -u 1 \"src/**/*.{html,css,scss,ttf,woff,woff2,eot,svg,jpg,png}\" dist/", "generate:types": "cross-env PAYLOAD_CONFIG_PATH=src/payload.config.ts payload generate:types", "generate:graphQLSchema": "cross-env PAYLOAD_CONFIG_PATH=src/payload.config.ts payload generate:graphQLSchema", "payload": "cross-env PAYLOAD_CONFIG_PATH=src/payload.config.ts payload"}, "dependencies": {"@payloadcms/bundler-webpack": "^1.0.0", "@payloadcms/db-mongodb": "^1.0.0", "@payloadcms/plugin-cloud": "^3.0.0", "@payloadcms/richtext-slate": "^1.0.0", "cross-env": "^7.0.3", "dotenv": "^8.2.0", "express": "^4.19.2", "payload": "^2.0.0"}, "devDependencies": {"@types/express": "^4.17.9", "copyfiles": "^2.4.1", "nodemon": "^2.0.6", "ts-node": "^9.1.1", "typescript": "^4.8.4"}}