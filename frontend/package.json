{"name": "website", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3001", "build": "next build", "start": "next start -p 3001", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^3.6.0", "@payloadcms/richtext-lexical": "^0.11.1", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-toast": "^1.2.1", "@uploadthing/react": "^6.6.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "embla-carousel": "^8.1.5", "embla-carousel-fade": "^8.1.5", "embla-carousel-react": "^8.1.5", "escape-html": "^1.0.3", "input-otp": "^1.2.4", "keen-slider": "^6.8.6", "lucide-react": "^0.395.0", "moment": "^2.30.1", "next": "14.2.4", "next-themes": "^0.3.0", "react": "^18", "react-dom": "^18", "react-hook-form": "^7.52.0", "slate": "^0.103.0", "sonner": "^1.5.0", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.23.8"}, "devDependencies": {"@types/escape-html": "^1.0.4", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.4", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}