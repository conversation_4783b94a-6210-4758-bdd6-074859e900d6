import Image from "next/image";

export default function ServicesSection() {
  const data = [
    {
      color: "#256FFF",
      title: "Our Services",
      desc: "Love games? We've got you covered! We make cool 2D/3D adventures and web games that pop. Whether you play on PC or mobile, our Unity and Unreal experts have you covered. We'll even spruce up your favorite games or fix any bugs. Let's level up your gaming fun!",
      img: "/services/cover.png",
      button: {
        title: "Services",
        href: "/services",
      },
    },
    {
      color: "#FF0000",
      title: "Career at Henmova",
      desc: "We want to provide you with a platform to grow and excel as an individual, unleash your potential and make an impact in your region We want to provide you with a platform to grow and excel as an individual, unleash your potential and make an impact in your region",
      img: "/services/cover.png",
      button: {
        title: "Apply Today",
        href: "/jobs",
      },
    },
  ];

  return (
    <div className="container mx-auto p-4 space-y-8 md:px-8">
      <div className="md:flex-row-reverse hidden"></div>
      {data.map((item, index) => (
        <div
          key={index}
          className={
            "flex flex-col items-center gap-4 md:flex-row" +
            (index % 2 === 0 ? "-reverse" : "")
          }
        >
          <div className="">
            <Image
              src={item.img}
              alt={item.title}
              className="w-full rounded-md"
              width={500}
              height={500}
            />
          </div>
          <div className="md:w-1/2 p-4 md:p-8">
            <h1
              className={`text-3xl sm:text-4xl font-bold mb-8`}
              style={{
                color: item.color,
              }}
            >
              {item.title}
            </h1>
            <p className="text-lg">{item.desc}</p>
            <div className="mt-6">
              <a
                href={item.button.href}
                style={{
                  backgroundColor: item.color,
                }}
                className={`px-5 py-2 text-lg text-white`}
              >
                {item.button.title}
              </a>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}
