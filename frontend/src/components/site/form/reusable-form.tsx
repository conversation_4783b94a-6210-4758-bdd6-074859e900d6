import React from "react";
import {
  useForm,
  UseFormReturn,
  FieldValues,
  DefaultValues,
} from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

export interface FormFieldConfig {
  name: string;
  label: string;
  type: "text" | "email" | "password" | "textarea" | "checkbox" | "select";
  placeholder?: string;
  description?: string;
  options?: { value: string; label: string }[];
}

interface ReusableFormProps<T extends FieldValues> {
  schema: z.ZodType<T>;
  onSubmit: (data: T) => void;
  fields: FormFieldConfig[];
  defaultValues?: DefaultValues<T>;
}

function ReusableForm<T extends FieldValues>({
  schema,
  onSubmit,
  fields,
  defaultValues,
}: ReusableFormProps<T>) {
  const form = useForm<T>({
    resolver: zodResolver(schema),
    defaultValues,
  });

  const renderField = (field: FormFieldConfig, formField: any) => {
    switch (field.type) {
      case "textarea":
        return <Textarea placeholder={field.placeholder} {...formField} />;
      case "checkbox":
        return (
          <FormControl>
            <Checkbox
              checked={formField.value}
              onCheckedChange={formField.onChange}
            />
          </FormControl>
        );
      case "select":
        return (
          <Select
            onValueChange={formField.onChange}
            defaultValue={formField.value}
          >
            <FormControl>
              <SelectTrigger>
                <SelectValue placeholder={field.placeholder} />
              </SelectTrigger>
            </FormControl>
            <SelectContent>
              {field.options?.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );
      default:
        return (
          <Input
            type={field.type}
            placeholder={field.placeholder}
            {...formField}
          />
        );
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        {fields.map((field) => (
          <FormField
            key={field.name}
            control={form.control}
            // @ts-ignore
            name={field.name}
            render={({ field: formField }) => (
              <FormItem>
                <FormLabel>{field.label}</FormLabel>
                <FormControl>{renderField(field, formField)}</FormControl>
                {field.description && (
                  <FormDescription>{field.description}</FormDescription>
                )}
                <FormMessage />
              </FormItem>
            )}
          />
        ))}
        <Button type="submit">Submit</Button>
      </form>
    </Form>
  );
}

export default ReusableForm;
