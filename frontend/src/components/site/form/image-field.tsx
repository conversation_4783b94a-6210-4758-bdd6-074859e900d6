// "use client";

// import { Button } from "@/components/ui/button";
// import { InputProps } from "@/components/ui/input";
// import { UploadButton } from "@/utils/uploadthing";

// export type ImageFieldProps = InputProps & {
//   setUrl: (url: string) => void;
// };

// export default function ImageField(props: ImageFieldProps) {
//   return (
//     <div className="">
//       {props.value ? (
//         <div className="space-y-2">
//           <img src={props.value as string} className="w-full" />
//           <Button variant="destructive" onClick={() => props.setUrl("")}>
//             Remove
//           </Button>
//         </div>
//       ) : (
//         <UploadButton
//           endpoint="imageUploader"
//           onClientUploadComplete={(res) => {
//             props.setUrl(res[0].url);
//           }}
//           onUploadError={(error: Error) => {
//             // Do something with the error.
//             alert(`ERROR! ${error.message}`);
//           }}
//         />
//       )}
//     </div>
//   );
// }

// // "use client";

// // import { Button } from "@/components/ui/button";
// // import { Input, InputProps } from "@/components/ui/input";
// // import { useState } from "react";

// // export type ImageFieldProps = InputProps;

// // export default function ImageField(props: ImageFieldProps) {
// //   const [src, setSrc] = useState<string>((props.value as string) || "");

// //   return (
// //     <>
// //       {src ? (
// //         <div className="">
// //           <img src={src} className="w-full" />
// //           <Button variant="destructive" onClick={() => setSrc("")}>
// //             Remove
// //           </Button>
// //         </div>
// //       ) : (
// //         <Input
// //           type="file"
// //           // onChange={(e) => setSrc("http://127.0.0.1:3000/logos/logo-2.svg")}
// //           {...props}
// //           value={""}
// //         />
// //       )}
// //     </>
// //   );
// // }
