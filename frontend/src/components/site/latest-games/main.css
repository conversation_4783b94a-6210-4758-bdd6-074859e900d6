.embla2 {
  margin: auto;
  --slide-height: 15rem;
  --slide-spacing: 0.1rem;
  --slide-size: 35%;
}

@media (max-width: 639px) {
  .embla2 {
    --slide-size: 100%;
  }
}

.embla2__viewport {
  overflow: hidden;
}
.embla2__container {
  backface-visibility: hidden;
  display: flex;
  touch-action: pan-y pinch-zoom;
  margin-left: calc(var(--slide-spacing) * -1);
}
.embla2__slide {
  flex: 0 0 var(--slide-size);
  min-width: 0;
  padding-left: var(--slide-spacing);
}
.embla2__slide__number {
  display: flex;
  align-items: center;
  justify-content: center;
  height: var(--slide-height);
}
.embla2__controls {
  display: grid;
  grid-template-columns: auto 1fr;
  justify-content: space-between;
  gap: 1.2rem;
  margin-top: 1.8rem;
}
/* .embla2__buttons {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.6rem;
    align-items: center;
  } */
.embla2__button {
  -webkit-tap-highlight-color: rgba(49, 49, 49, 0.5);
  -webkit-appearance: none;
  appearance: none;
  background-color: transparent;
  touch-action: manipulation;
  display: inline-flex;
  text-decoration: none;
  cursor: pointer;
  border: 0;
  padding: 0;
  margin: 0;
  box-shadow: inset 0 0 0 0.2rem rgb(234, 234, 234);
  width: 3.6rem;
  height: 3.6rem;
  z-index: 1;
  border-radius: 50%;
  color: rgb(54, 49, 61);
  display: flex;
  align-items: center;
  justify-content: center;
}
.embla2__button:disabled {
  color: rgb(192, 192, 192);
}
.embla2__button__svg {
  width: 35%;
  height: 35%;
}
.embla2__dots {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-end;
  align-items: center;
  margin-right: calc((2.6rem - 1.4rem) / 2 * -1);
}
.embla2__dot {
  -webkit-tap-highlight-color: rgba(49, 49, 49, 0.5);
  -webkit-appearance: none;
  appearance: none;
  background-color: transparent;
  touch-action: manipulation;
  display: inline-flex;
  text-decoration: none;
  cursor: pointer;
  border: 0;
  padding: 0;
  margin: 0;
  width: 1.6rem;
  height: 1.6rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}
.embla2__dot:after {
  box-shadow: inset 0 0 0 0.1rem rgb(234, 234, 234);
  width: 1rem;
  height: 1rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  content: "";
}
.embla2__dot--selected:after {
  box-shadow: inset 0 0 0 0.15rem rgb(234, 234, 234);
  background-color: rgb(234, 234, 234);
  /* box-shadow: inset 0 0 0 0.2rem rgb(54, 49, 61); */
}
