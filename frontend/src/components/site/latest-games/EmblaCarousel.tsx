import React, { useCallback, useEffect, useRef } from "react";
import {
  EmblaCarouselType,
  EmblaEventType,
  EmblaOptionsType,
} from "embla-carousel";
import useEmblaCarousel from "embla-carousel-react";
import {
  NextButton,
  PrevButton,
  usePrevNextButtons,
} from "./EmblaCarouselArrowButtons";
import { DotButton, useDotButton } from "./EmblaCarouselDotButton";
import { Game } from "@/types/games";
import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";

const TWEEN_FACTOR_BASE = 0.52;

const numberWithinRange = (number: number, min: number, max: number): number =>
  Math.min(Math.max(number, min), max);

type PropType = {
  slides: Game[];
  options?: EmblaOptionsType;
  setBgImg: (bgImg: string) => void;
};

const EmblaCarousel: React.FC<PropType> = (props) => {
  const { slides, options } = props;
  const [emblaRef, emblaApi] = useEmblaCarousel(options);
  const tweenFactor = useRef(0);
  const tweenNodes = useRef<HTMLElement[]>([]);

  const { selectedIndex, scrollSnaps, onDotButtonClick } =
    useDotButton(emblaApi);

  const {
    prevBtnDisabled,
    nextBtnDisabled,
    onPrevButtonClick,
    onNextButtonClick,
  } = usePrevNextButtons(emblaApi);

  const setTweenNodes = useCallback((emblaApi: EmblaCarouselType): void => {
    tweenNodes.current = emblaApi.slideNodes().map((slideNode) => {
      return slideNode.querySelector(".embla2__slide__number") as HTMLElement;
    });
  }, []);

  const setTweenFactor = useCallback((emblaApi: EmblaCarouselType) => {
    tweenFactor.current = TWEEN_FACTOR_BASE * emblaApi.scrollSnapList().length;
  }, []);

  const tweenScale = useCallback(
    (emblaApi: EmblaCarouselType, eventName?: EmblaEventType) => {
      const engine = emblaApi.internalEngine();
      const scrollProgress = emblaApi.scrollProgress();
      const slidesInView = emblaApi.slidesInView();
      const isScrollEvent = eventName === "scroll";

      emblaApi.scrollSnapList().forEach((scrollSnap, snapIndex) => {
        let diffToTarget = scrollSnap - scrollProgress;
        const slidesInSnap = engine.slideRegistry[snapIndex];

        slidesInSnap.forEach((slideIndex) => {
          if (isScrollEvent && !slidesInView.includes(slideIndex)) return;

          if (engine.options.loop) {
            engine.slideLooper.loopPoints.forEach((loopItem) => {
              const target = loopItem.target();

              if (slideIndex === loopItem.index && target !== 0) {
                const sign = Math.sign(target);

                if (sign === -1) {
                  diffToTarget = scrollSnap - (1 + scrollProgress);
                }
                if (sign === 1) {
                  diffToTarget = scrollSnap + (1 - scrollProgress);
                }
              }
            });
          }

          const tweenValue = 1 - Math.abs(diffToTarget * tweenFactor.current);
          const scale = numberWithinRange(tweenValue, 0, 1).toString();
          const tweenNode = tweenNodes.current[slideIndex];
          tweenNode.style.transform = `scale(${scale})`;
        });
      });
    },
    []
  );

  const handleSlideChange = useCallback(
    (index: number) => {
      onDotButtonClick(index);
      const _img = document.getElementById(
        slides[index].id.toString()
      ) as HTMLImageElement;
      props.setBgImg(_img.currentSrc);
    },
    [onDotButtonClick, slides, props]
  );

  useEffect(() => {
    if (!emblaApi) return;

    setTweenNodes(emblaApi);
    setTweenFactor(emblaApi);
    tweenScale(emblaApi);

    emblaApi
      .on("reInit", setTweenNodes)
      .on("reInit", setTweenFactor)
      .on("reInit", tweenScale)
      .on("scroll", tweenScale)
      .on("slideFocus", tweenScale)
      .on("select", () => {
        const currentIndex = emblaApi.selectedScrollSnap();
        handleSlideChange(currentIndex);
      });

    return () => {
      emblaApi.off("select", () => {});
    };
  }, [emblaApi, handleSlideChange, setTweenFactor, setTweenNodes, tweenScale]);

  return (
    <div
      className="mb-[calc(14vh-4rem)] sm:mb-[2rem]"
      style={{ userSelect: "none" }}
    >
      <div className="embla2">
        <div className="embla2__viewport" ref={emblaRef}>
          <div className="embla2__container">
            {slides.map((game, index) => (
              <div
                className="embla2__slide group"
                onClick={() => handleSlideChange(index)}
                key={index}
              >
                <div className="embla2__slide__number overflow-hidden">
                  <div
                    className={`flex items-end h-full w-full absolute transition-all duration-500 ease-in-out text-lg z-30 group-hover:bottom-0 bottom-[-100%]`}
                  >
                    <div className="bg-black/65 backdrop-blur-sm p-2 w-full gap-1 flex flex-col items-start justify-between">
                      <Link
                        href={`/games/${game.slug}`}
                        className="hover:underline font-semibold"
                      >
                        {game.title}
                      </Link>
                      <div className="text-xs font-normal">{game.tagline}</div>
                    </div>
                  </div>
                  <Image
                    id={game.id.toString()}
                    className="object-cover rounded-sm group-hover:rounded-md transition-all duration-500 absolute z-10"
                    src={`${game.coverImage.url}`}
                    alt={game.coverImage.alt}
                    fill
                  />
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="embla2__controls">
          {/* <div className="embla2__buttons">
          <PrevButton onClick={onPrevButtonClick} disabled={prevBtnDisabled} />
          <NextButton onClick={onNextButtonClick} disabled={nextBtnDisabled} />
        </div> */}

          <div className="absolute left-1/2 -translate-x-1/2">
            <div className="embla2__dots">
              {scrollSnaps.map((_, index) => (
                <DotButton
                  key={index}
                  onClick={() => handleSlideChange(index)}
                  className={"embla2__dot".concat(
                    index === selectedIndex ? " embla2__dot--selected" : ""
                  )}
                />
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EmblaCarousel;
