"use client";

import React, { useState } from "react";
import EmblaCarousel from "./EmblaCarousel";
import { EmblaOptionsType } from "embla-carousel";
import "./main.css";
import { Game } from "@/types/games";

const OPTIONS: EmblaOptionsType = { loop: true };

export default function LatestGames({ games }: { games: Game[] }) {
  const [bgImg, setBgImg] = useState<string>(
    games[0].coverImage.url
    // `${
    //   process.env.NEXT_PUBLIC_DOMAIN
    // }/_next/image?url=${games[0].coverImage.url.replace(
    //   /\//g,
    //   "%2F"
    // )}&w=1920&q=75`
  );

  if (!games || games.length < 4) return null;

  return (
    <div className="w-full py-6 relative overflow-hidden">
      <div
        className={`absolute inset-0 scale-125 blur-3xl transition-all ease-in-out duration-500`}
        style={{
          backgroundImage: `radial-gradient(circle,rgba(0,0,0,.15),rgba(0,0,0,.85)),url("${bgImg}")`,
          backgroundRepeat: "no-repeat",
          backgroundAttachment: "fixed",
          backgroundBlendMode: "darken",
          backgroundSize: "cover",
          backgroundPosition: "center",
        }}
      />
      <div className="container mx-auto p-4 md:px-8 text-white">
        <h1 className="sm:text-4xl text-2xl font-bold mb-8 text-center">
          Latest Games
        </h1>
        <EmblaCarousel slides={games} setBgImg={setBgImg} options={OPTIONS} />
      </div>
    </div>
  );
}
