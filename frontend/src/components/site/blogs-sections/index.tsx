import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/card";
import { APIResponse } from "@/types/api";
import { Blog } from "@/types/blogs";
import { Calendar } from "lucide-react";
import moment from "moment";
import Image from "next/image";
import Link from "next/link";

export default async function BlogsSection() {
  const fetchData = async (): Promise<APIResponse<Blog>> => {
    try {
      const res = await fetch(
        `/api/blogs?limit=3`
      );
      const data = await res.json();
      return data;
    } catch (error) {
      console.log(error);
      return null;
    }
  };

  const res = await fetchData();

  return (
    <div className="bg-black/5 border-t-2 border-t-black/5 w-full py-8">
      <div className="container mx-auto p-4 md:px-8 text-white">
        {/* <div className="text-lg font-semibold pb-4">Top Blogs</div> */}
        <div className="grid grid-cols-1 md:grid-cols-3 mx-auto gap-4">
          {res?.docs?.map((item, index) => (
            <Card key={index}>
              <CardHeader className="p-1">
                <CardTitle>
                  {item.coverImage && (
                    <Image
                      src={`${item.coverImage.url}`}
                      alt={item.coverImage.alt}
                      width={item.coverImage.width}
                      height={item.coverImage.height}
                      className="rounded-md w-full h-full object-cover aspect-video"
                    />
                  )}
                </CardTitle>
              </CardHeader>
              <CardContent className="px-2 pb-3 pt-2">
                <div className="text-xl font-medium">{item.title}</div>
              </CardContent>
              <CardFooter className="p-2 flex justify-between items-center border-t-black/10 border-t-2">
                <div className="text-sm text-black/40 flex items-center gap-2">
                  <Calendar size={18} />
                  {moment(item.createdAt).format("Do MMMM YYYY")}
                </div>
                <Button variant="link">
                  <Link href={`/news/${item.slug}`}>Read More</Link>
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
}
