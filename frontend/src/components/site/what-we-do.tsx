import Link from "next/link";
import { But<PERSON> } from "../ui/button";
import Image from "next/image";

import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

export function MyCard({
  title,
  desc,
  img,
}: {
  title: string;
  desc: string;
  img: string;
}) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>
          <Image
            className="mx-auto sm:w-40 w-28"
            src={"/temp-img.png"}
            alt={title}
            width={100}
            height={100}
          />
        </CardTitle>
      </CardHeader>
      <CardContent>
        <h2 className="text-xl font-bold text-primary mb-2">{title}</h2>
        <p className="text-sm text-muted-foreground line-clamp-4 text-pretty">
          {desc}
        </p>
      </CardContent>
    </Card>
    // <div className="bg-white rounded-lg shadow-md p-6 max-w-sm mx-auto text-center">
    //   <div className="mb-4 mx-auto w-fit">
    //     <Image src={img} alt={title} width={100} height={100} />
    //   </div>
    //   <h2 className="text-xl font-bold text-gray-800 mb-2">Esports</h2>
    //   <p className="text-sm text-gray-600">
    //     Garena is a leading advocate and organizer of Esports events in Greater
    //     Southeast Asia, which strengthens our game ecosystem and increases user
    //     engagement
    //   </p>
    // </div>
  );
}

export function WhatWeDo() {
  const description =
    "ham games bbnane he or pata nhi kya karte he or ham client ke kam karte he and gamedev jobs bhi provide karte he ham games bbnane he or pata nhi kya karte he or ham client ke kam karte he and gamedev jobs bhi provide karte he";

  const data = [
    {
      title: "Games",
      img: "/icons/game.png",
      desc: description,
    },
    {
      title: "Services",
      img: "/icons/services.png",
      desc: description,
    },
    {
      title: "Community",
      img: "/icons/community.png",
      desc: description,
    },
  ];

  return (
    <div className="container mx-auto p-4 md:px-8 my-4 text-center">
      <h1 className="sm:text-4xl text-2xl font-bold mb-8">What We Do</h1>
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
        {data.map((item) => (
          <MyCard key={item.title} {...item} />
        ))}
      </div>
    </div>
  );
}
