import React, { Fragment } from "react";
import escapeHTML from "escape-html";
import { Text } from "slate";
import { <PERSON>ert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import Image from "next/image";
import Link from "next/link";

const serialize = (children: any[]) =>
  children.map((node, i) => {
    if (Text.isText(node)) {
      let text = (
        <span dangerouslySetInnerHTML={{ __html: escapeHTML(node.text) }} />
      );
      // @ts-ignore
      if (node.bold) {
        text = (
          <strong key={i} className="font-bold">
            {text}
          </strong>
        );
      }
      // @ts-ignore
      if (node.code) {
        text = (
          <code
            key={i}
            className="bg-gray-100 rounded px-1 py-0.5 font-mono text-sm"
          >
            {text}
          </code>
        );
      }
      // @ts-ignore
      if (node.italic) {
        text = (
          <em key={i} className="italic">
            {text}
          </em>
        );
      }

      if (node.text === "") {
        text = <br />;
      }

      // Handle other leaf types here...

      return <Fragment key={i}>{text}</Fragment>;
    }

    if (!node) {
      return null;
    }

    switch (node.type) {
      case "h1":
        return (
          <h1 key={i} className="text-4xl font-bold mb-4 mt-6">
            {serialize(node.children)}
          </h1>
        );
      case "h2":
        return (
          <h2 key={i} className="text-3xl font-semibold mb-3 mt-5">
            {serialize(node.children)}
          </h2>
        );
      case "h3":
        return (
          <h3 key={i} className="text-2xl font-semibold mb-2 mt-4">
            {serialize(node.children)}
          </h3>
        );
      case "h4":
        return (
          <h4 key={i} className="text-xl font-semibold mb-2 mt-3">
            {serialize(node.children)}
          </h4>
        );
      case "h5":
        return (
          <h5 key={i} className="text-lg font-semibold mb-2 mt-3">
            {serialize(node.children)}
          </h5>
        );
      case "h6":
        return (
          <h6 key={i} className="text-base font-semibold mb-2 mt-3">
            {serialize(node.children)}
          </h6>
        );
      case "upload":
        return (
          <Image
            className="w-full rounded-md"
            key={i}
            src={`${node.value.url}`}
            alt={node.value.alt}
            width={node.value.width}
            height={node.value.height}
          />
        );
      case "blockquote":
        return (
          <Alert
            key={i}
            className="my-4 bg-gray-100 border-l-4 border-gray-500 italic"
          >
            <AlertDescription>{serialize(node.children)}</AlertDescription>
          </Alert>
        );
      case "ul":
        return (
          <ul key={i} className="list-disc pl-5 mb-4">
            {serialize(node.children)}
          </ul>
        );
      case "ol":
        return (
          <ol key={i} className="list-decimal pl-5 mb-4">
            {serialize(node.children)}
          </ol>
        );
      case "li":
        return (
          <li key={i} className="mb-1">
            {serialize(node.children)}
          </li>
        );
      case "link":
        return (
          <Link
            href={escapeHTML(node.url)}
            className="text-blue-600 hover:underline"
          >
            {serialize(node.children)}
          </Link>
        );
      default:
        return (
          <p key={i} className="mb-4">
            {serialize(node.children)}
          </p>
        );
    }
  });

export default serialize;
