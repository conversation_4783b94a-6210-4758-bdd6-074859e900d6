"use client";

import React, { useCallback, useEffect, useState } from "react";
import { type Slide } from "@/types/home";
import { useKeenSlider } from "keen-slider/react";
import "keen-slider/keen-slider.min.css";
import "./styles.css";
import Image from "next/image";
import Link from "next/link";
import { ChevronLeft, ChevronRight } from "lucide-react";

const Carousel = ({ slides }: { slides: Slide[] }) => {
  const [currentIndex, setCurrentIndex] = useState(0);

  const nextSlide = useCallback(() => {
    setCurrentIndex((prevIndex) => (prevIndex + 1) % slides.length);
  }, [slides.length]);

  const prevSlide = () => {
    setCurrentIndex(
      (prevIndex) => (prevIndex - 1 + slides.length) % slides.length
    );
  };

  useEffect(() => {
    const interval = setInterval(nextSlide, 5000); // Auto-advance every 5 seconds
    return () => clearInterval(interval);
  }, [nextSlide]);

  return (
    <div className="relative h-[40rem] sm:h-[38rem] max-w-[100rem] mx-auto mb-[2rem] overflow-hidden">
      {slides.map((slide, index) => (
        <div
          key={index}
          className={`absolute inset-0 transition-opacity duration-500 ${
            index === currentIndex ? "opacity-100" : "opacity-0"
          }`}
        >
          <Image
            src={`${slide.bgImage.url}`}
            alt={slide.bgImage.alt}
            fill
            style={{ objectFit: "cover" }}
          />
          <div className="absolute bottom-0 left-0 right-0 flex flex-col justify-center items-center gap-3">
            <Image
              className={`absolute left-1/2 -translate-x-1/2 transition-all ease-in-out duration-500 w-56 ${
                index === currentIndex
                  ? "bottom-[14rem] opacity-100"
                  : "bottom-[10rem] opacity-0"
              }`}
              src={`${slide.titleImage.url}`}
              alt={slide.titleImage.alt}
              width={slide.titleImage.width}
              height={slide.titleImage.height}
            />
            <div className="absolute bottom-[10rem] left-1/2 -translate-x-1/2 text-white/75 font-normal w-[80%] sm:max-w-md line-clamp-2 text-center">
              {slide.desc}
            </div>
            <Link
              href={slide.btnLink}
              className={`absolute bottom-[6rem] left-1/2 -translate-x-1/2 px-4 py-2`}
            >
              <Image
                src={`${slide.btnImage.url}`}
                alt={slide.btnImage.alt}
                width={slide.btnImage.width}
                height={slide.btnImage.height}
              />
            </Link>
          </div>
        </div>
      ))}
      <button
        onClick={prevSlide}
        className="absolute top-1/2 left-4 transform -translate-y-1/2 bg-white bg-opacity-50 rounded-full p-2 hover:bg-opacity-75 transition-all"
      >
        <ChevronLeft className="w-6 h-6" />
      </button>
      <button
        onClick={nextSlide}
        className="absolute top-1/2 right-4 transform -translate-y-1/2 bg-white bg-opacity-50 rounded-full p-2 hover:bg-opacity-75 transition-all"
      >
        <ChevronRight className="w-6 h-6" />
      </button>
      <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
        {slides.map((_, index) => (
          <button
            key={index}
            onClick={() => setCurrentIndex(index)}
            className={`w-3 h-3 rounded-full ${
              index === currentIndex ? "bg-white" : "bg-gray-400"
            }`}
          />
        ))}
      </div>
    </div>
  );
};

export default Carousel;

export function Component({ slides }: { slides: Slide[] }) {
  const [opacities, setOpacities] = useState<number[]>([]);

  const [sliderRef] = useKeenSlider({
    slides: slides.length,
    loop: true,
    detailsChanged(s) {
      const new_opacities = s.track.details.slides.map(
        (slide) => slide.portion
      );
      setOpacities(new_opacities);
    },
  });

  return (
    <div ref={sliderRef} className="fader">
      {slides.map((slide, idx) => (
        <div
          key={idx}
          className="fader__slide"
          style={{ opacity: opacities[idx] }}
        >
          <Image
            className={`absolute left-1/2 -translate-x-1/2 transition-all ease-in-out duration-500 ${
              idx === idx
                ? "bottom-[14rem] opacity-100"
                : "bottom-[10rem] opacity-0"
            }`}
            src={`${slide.titleImage.url}`}
            alt={slide.titleImage.alt}
            width={slide.titleImage.width}
            height={slide.titleImage.height}
          />
          <div className="absolute bottom-[10rem] left-1/2 -translate-x-1/2 text-white/75 font-normal w-[80%] sm:max-w-md line-clamp-2 text-center">
            {slide.desc}
          </div>
          {slide.btnImage && (
            <Link
              href={slide.btnImage.url}
              className={`absolute bottom-[6rem] left-1/2 -translate-x-1/2 px-4 py-2`}
            >
              <Image
                src={`${slide.btnImage.url}`}
                alt={slide.btnImage.alt}
                width={slide.btnImage.width}
                height={slide.btnImage.height}
              />
            </Link>
          )}
          <Image
            className="sm:w-[calc(100rem)] w-full aspect-video h-full object-cover"
            // className="h-[calc(100vh_-_4rem)] sm:h-[41.4rem] w-full object-cover"
            src={`${slide.bgImage.url}`}
            alt={slide.bgImage.alt}
            width={slide.bgImage.width}
            height={slide.bgImage.height}
          />
          {/* <Image
            src={slide.bgImage.url}
            alt={slide.bgImage.alt}
            width={slide.bgImage.width}
            height={slide.bgImage.height}
          /> */}
        </div>
      ))}
    </div>
  );
}

// "use client";
// import "@/styles/embla.css";
// import Icon from "@/components/site/icon";
// import React from "react";
// import Fade from "embla-carousel-fade";
// import { EmblaOptionsType } from "embla-carousel";
// import { DotButton, useDotButton } from "./EmblaCarouselDotButton";
// import { usePrevNextButtons } from "./EmblaCarouselArrowButtons";
// import useEmblaCarousel from "embla-carousel-react";
// import { ArrowLeft } from "lucide-react";

// import Image from "next/image";
// import Link from "next/link";

// import { type Slide } from "@/types/home";

// export default function Component({ slides }: { slides: Slide[] }) {
//   const options: EmblaOptionsType = {
//     loop: true,
//     containScroll: false,
//   };

//   const [emblaRef, emblaApi] = useEmblaCarousel(options, [Fade()]);

//   const { selectedIndex, scrollSnaps, onDotButtonClick } =
//     useDotButton(emblaApi);

//   const {
//     prevBtnDisabled,
//     nextBtnDisabled,
//     onPrevButtonClick,
//     onNextButtonClick,
//   } = usePrevNextButtons(emblaApi);

//   return (
//     <div
//       className="mb-[calc(100vh-4rem)] sm:mb-[43rem]"
//       style={{
//         userSelect: "none",
//       }}
//     >
//       <div className="">
//         <section className="embla">
//           <div className="embla__viewport" ref={emblaRef}>
//             <div className="embla__container">
//               {slides.map((slide, index) => (
//                 <div className="embla__slide" key={index}>
//                   <Image
//                     className={`absolute left-1/2 -translate-x-1/2 transition-all ease-in-out duration-500 ${
//                       selectedIndex === index
//                         ? "bottom-[14rem] opacity-100"
//                         : "bottom-[10rem] opacity-0"
//                     }`}
//                     src={slide.titleImage.url}
//                     alt={slide.titleImage.alt}
//                     width={slide.titleImage.width}
//                     height={slide.titleImage.height}
//                   />
//                   <div className="absolute bottom-[10rem] left-1/2 -translate-x-1/2 text-white/75 font-normal w-[80%] sm:max-w-md line-clamp-2 text-center">
//                     {slide.desc}
//                   </div>
//                   {slide.btnImage && (
//                     <Link
//                       href={slide.btnImage.url}
//                       className={`absolute bottom-[6rem] left-1/2 -translate-x-1/2 px-4 py-2`}
//                     >
//                       <Image
//                         src={slide.btnImage.url}
//                         alt={slide.btnImage.alt}
//                         width={slide.btnImage.width}
//                         height={slide.btnImage.height}
//                       />
//                     </Link>
//                   )}
//                   <Image
//                     className="sm:w-[calc(100rem)] w-full aspect-video h-full object-cover"
//                     // className="h-[calc(100vh_-_4rem)] sm:h-[41.4rem] w-full object-cover"
//                     src={slide.bgImage.url}
//                     alt={slide.bgImage.alt}
//                     width={slide.bgImage.width}
//                     height={slide.bgImage.height}
//                   />
//                 </div>
//               ))}
//             </div>
//           </div>

//           <div className="embla__controls">
//             <div className="embla__buttons sm:flex hidden absolute top-1/2 -translate-y-1/2 justify-between left-1/2 -translate-x-1/2 max-w-screen-lg w-full">
//               <button
//                 className="bg-white hover:border-primary border-2 border-white transition-all ease-in-out p-2 rounded-full size-12 flex justify-center items-center"
//                 onClick={onPrevButtonClick}
//                 disabled={prevBtnDisabled}
//               >
//                 <ArrowLeft fill="white" stroke="red" size={36} />
//               </button>

//               <button
//                 className="bg-white hover:border-primary border-2 border-white transition-all ease-in-out p-2 rounded-full size-12 flex justify-center items-center"
//                 onClick={onNextButtonClick}
//                 disabled={nextBtnDisabled}
//               >
//                 <ArrowLeft
//                   fill="white"
//                   className="rotate-180"
//                   stroke="red"
//                   size={36}
//                 />
//               </button>
//             </div>

//             <div className="embla__dots absolute top-[calc(90%-1.5rem)] left-1/2 -translate-x-1/2">
//               {scrollSnaps.map((_, index) => (
//                 <DotButton
//                   key={index}
//                   onClick={() => onDotButtonClick(index)}
//                   className={"embla__dot".concat(
//                     index === selectedIndex ? " embla__dot--selected" : ""
//                   )}
//                 />
//               ))}
//             </div>
//           </div>
//         </section>
//       </div>
//     </div>
//   );
// }
