import { APIResponse } from "@/types/api";

export type Blog = {
  id: string;
  title: string;
  slug: string;
  content: any;
  coverImage: {
    id: string;
    alt: string;
    filename: string;
    mimeType: string;
    filesize: number;
    width: number;
    height: number;
    focalX: number;
    focalY: number;
    createdAt: string;
    updatedAt: string;
    url: string;
  };
  createdAt: string;
  updatedAt: string;
};

export type Response = APIResponse<Blog>;
