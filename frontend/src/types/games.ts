import { APIResponse } from "@/types/api";

export type GameLink = {
  platform: "App Store" | "Play Store" | "Epic" | "Steam";
  link: string;
};

export type Game = {
  id: string;
  title: string;
  tagline: string;
  content: any;
  links: GameLink[];
  platforms: string;
  artstyles: string;
  genres: string;
  coverImage: {
    id: string;
    alt: string;
    filename: string;
    mimeType: string;
    filesize: number;
    width: number;
    height: number;
    focalX: number;
    focalY: number;
    createdAt: string;
    updatedAt: string;
    url: string;
  };
  slug: string;
  createdAt: string;
  updatedAt: string;
};

export type Response = APIResponse<Game>;
