import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import Image from "next/image";
import { Job } from "./types";
import { Button } from "@/components/ui/button";

import moment from "moment";
import Link from "next/link";
import { Clock } from "lucide-react";

export const getJobTypeColor = (jobType: string) => {
  switch (jobType) {
    case "Full Time":
      return "bg-green-500";
    case "Part Time":
      return "bg-blue-500";
    case "Contract":
      return "bg-yellow-500";
    default:
      return "bg-gray-500";
  }
};

export default function JobCard({
  title,
  desc,
  slug,
  jobType,
  updatedAt,
}: Job) {
  const posted = moment(new Date(updatedAt)).fromNow();

  return (
    <Card className="p-0">
      <CardHeader className="p-4 pb-0">
        <div className="w-full justify-between items-center flex">
          <CardTitle>{title}</CardTitle>
          <div
            className={`text-white px-3 py-1 rounded-full ${getJobTypeColor(
              jobType
            )}`}
          >
            {jobType}
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-4">
        <div className="mb-3 line-clamp-3">{desc}</div>
        <div className="flex gap-2 items-end justify-between">
          <Button
            asChild
            variant={"default"}
            className="rounded-full px-5 py-[.5rem] font-bold"
          >
            <Link href={`/jobs/${slug}`}>Apply Now</Link>
          </Button>
          <div className="text-muted-foreground flex gap-2 items-center text-sm">
            <Clock size={18} /> {posted}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
