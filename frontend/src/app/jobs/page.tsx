import Title from "@/components/site/title";
import JobCard from "./card";
import { APIResponse } from "@/types/api";
import { Job } from "./types";

export const revalidate = 60 * 5;

export default async function Page() {
  const fetchData = async (): Promise<APIResponse<Job>> => {
    try {
      const res = await fetch(
        `/api/joblisting`
      );
      const data = await res.json();
      return data;
    } catch (error) {
      console.log(error);
      return null;
    }
  };

  const res = await fetchData();

  return (
    <div className="w-full mt-[-1px] space-y-6 mb-6">
      <Title className="font-extrabold py-4 text-primary">
        Career Opportunities!
      </Title>
      <div className="w-[94%] sm:container mx-auto space-y-4">
        <div className="text-xl text-muted-foreground -mb-2">
          Available Jobs
        </div>
        {res?.docs?.map((item) => (
          <JobCard key={item.id} {...item} />
        ))}
      </div>
    </div>
  );
}
