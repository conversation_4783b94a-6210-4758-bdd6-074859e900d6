import { APIResponse } from "@/types/api";
import { Job } from "../types";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { CalendarIcon, ClockIcon, MapPinIcon } from "lucide-react";
import serialize from "@/components/site/render-richtext";
import moment from "moment";

import { getJobTypeColor } from "../card";
import ApplyForm from "./apply-form";

export const revalidate = 60 * 5;

export default async function JobsDetailPage({
  params: { slug },
}: {
  params: { slug: string };
}) {
  const fetchData = async (): Promise<Job | null> => {
    try {
      const res = await fetch(
        `/api/joblisting/slug/${slug}`
      );
      const data = await res.json();
      return data;
    } catch (error) {
      console.log(error);
      return null;
    }
  };

  const res = await fetchData();

  if (!res) return null;

  const job = res;

  if (!job) return null;

  return (
    <div
      className="w-full mt-[-1px] space-y-6 mb-6"
      // className="container mx-auto p-4 md:px-8"
    >
      <div className="container mx-auto px-4 py-8">
        <Card className="w-full max-w-4xl mx-auto">
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle className="text-3xl font-bold">{job.title}</CardTitle>
              <div
                className={`px-3 py-1 rounded-full ${getJobTypeColor(
                  job.jobType
                )} text-white`}
              >
                {job.jobType.replace("-", " ")}
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="mb-6">
              <p className="text-gray-600 mb-4">{job.desc}</p>
              <div className="flex space-x-4 text-sm text-gray-500">
                <div className="flex items-center">
                  <CalendarIcon className="w-4 h-4 mr-1" />
                  <span>{moment(job.createdAt).format("LL")}</span>
                </div>
                <div className="flex items-center">
                  <MapPinIcon className="w-4 h-4 mr-1" />
                  <span>Remote</span>
                </div>
              </div>
            </div>
            <hr className="my-6" />
            <article className="w-full">
              <div className="w-full text-3xl font-bold">About the Job</div>
              {serialize(job.content || "")}
            </article>
            <hr className="my-6" />
            <div className="text-3xl font-bold mb-4">Apply now</div>
            <ApplyForm jobId={job.id} />
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
