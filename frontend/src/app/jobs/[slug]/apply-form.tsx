"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { But<PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import { useRef } from "react";
import PhoneInput from "@/components/site/phone-input";

export default function ApplyForm({ jobId }: { jobId: string }) {
  const router = useRouter();

  const fileInputRef = useRef<HTMLInputElement>(null);

  const formSchema = z.object({
    name: z.string().min(2, {
      message: "Name must be at least 2 characters.",
    }),
    email: z.string().email({ message: "Invalid email address." }),
    phone: z.object({
      countryCode: z.string().min(2, "Country code is required"),
      number: z.string().regex(/^\d{10}$/, "Phone number must be 10 digits"),
    }),
    portfolio: z.string().url({ message: "Invalid URL." }),
    file: z
      .any()
      .refine(() => fileInputRef.current?.files?.length, {
        message: "Please upload your resume.",
      })
      .refine(() => (fileInputRef.current?.files?.[0]?.size || 0) < 10000000, {
        message: "Resume size must be less than 10MB.",
      }),
  });

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      phone: {
        countryCode: "+91",
        number: "",
      },
    },
  });

  async function onSubmit(values: z.infer<typeof formSchema>) {
    toast.loading("Saving...");
    try {
      const file = fileInputRef.current?.files?.[0];
      const formData = new FormData();
      formData.append("name", values.name);
      formData.append("email", values.email);
      formData.append("mobile", values.phone.countryCode + values.phone.number);
      formData.append("portfolio", values.portfolio);
      formData.append("file", file as Blob);
      formData.append("job", jobId);
      const res = await fetch(
        `/api/jobapplications`,
        {
          method: "POST",
          body: formData,
        }
      );
      const data = await res.json();
      console.log(data);

      if (data.errors) {
        toast.error(data.errors[0].message);
        return;
      }

      toast.dismiss();
      toast.success("Thank you for applying!");
      router.push("/");
    } catch (e) {
      toast.error("Something went wrong");
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Name</FormLabel>
              <FormControl>
                <Input placeholder="" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email</FormLabel>
              <FormControl>
                <Input type="email" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <PhoneInput form={form} />

        <FormField
          control={form.control}
          name="portfolio"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Portfolio</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="file"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Resume</FormLabel>
              <FormControl>
                <Input
                  ref={fileInputRef}
                  onChange={(e) => {
                    field.onChange(e.target.files?.[0]);
                  }}
                  type="file"
                  accept="application/pdf"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button type="submit" className="w-full">
          Submit
        </Button>
      </form>
    </Form>
  );
}
