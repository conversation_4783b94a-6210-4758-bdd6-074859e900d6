import Title from "@/components/site/title";
import { APIResponse } from "@/types/api";
import { Blog } from "@/types/blogs";
import List from "./list";

export default async function Page() {
  const fetchData = async (): Promise<APIResponse<Blog>> => {
    try {
      const res = await fetch(`/api/blogs`);
      const data = await res.json();
      return data;
    } catch (error) {
      console.log(error);
      return null;
    }
  };

  const res = await fetchData();

  return (
    <div className="container mx-auto p-4 md:px-8">
      <Title className="pb-6">News</Title>
      {res?.docs && <List blogs={res.docs} />}
    </div>
  );
}
