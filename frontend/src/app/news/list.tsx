import { Button } from "@/components/ui/button";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/card";
import { Blog } from "@/types/blogs";
import { Clock } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

export default function List({ blogs }: { blogs: Blog[] }) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {blogs.map((item) => (
        <Card key={item.id} className="overflow-hidden">
          <CardHeader className="p-0">
            <CardTitle>
              {item.coverImage && (
                <Image
                  src={`${item.coverImage.url}`}
                  alt={item.coverImage.alt}
                  width={item.coverImage.width}
                  height={item.coverImage.height}
                  className="w-full h-full object-cover aspect-video"
                />
              )}
            </CardTitle>
          </CardHeader>
          <CardContent className="px-2 pb-3 pt-2">
            <div className="text-xl font-medium">{item.title}</div>
          </CardContent>
          <CardFooter className="p-2 flex justify-between items-center border-t-black/10 border-t-2">
            <div className="text-sm text-black/40 flex items-center gap-4">
              {new Date(item.createdAt)
                .toDateString()
                .split(" ")
                .slice(1)
                .join(" ")}
              <div className="flex items-center gap-1">
                <Clock size={17} /> 9 min
              </div>
            </div>
            <Button variant="link">
              <Link href={`/news/${item.slug}`}>Read More</Link>
            </Button>
          </CardFooter>
        </Card>
      ))}
    </div>
  );
}
