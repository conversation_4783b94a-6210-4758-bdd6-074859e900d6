import serialize from "@/components/site/render-richtext";
import Title from "@/components/site/title";
import { Blog } from "@/types/blogs";

export const revalidate = 60 * 5;

export default async function Page({ params }: { params: { slug: string } }) {
  const fetchData = async (slug: string): Promise<Blog | null> => {
    try {
      const res = await fetch(
        `/api/blogs/slug/${slug}`
      );
      const data = await res.json();
      return data;
    } catch (error) {
      console.log(error);
      return null;
    }
  };

  const data = await fetchData(params.slug);

  return (
    <div className="container mx-auto p-4 md:px-8 bg-white">
      <Title className="font-semibold mt-4 text-primary mb-6">
        {data?.title}
      </Title>
      <hr className="my-4" />
      <article className="w-full sm:w-[60%] py-1 px-4 mx-auto bg-[#F9FCFF] border rounded-md">
        {serialize(data?.content || "")}
      </article>
    </div>
  );
}
