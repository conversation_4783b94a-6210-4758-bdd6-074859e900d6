import { revalidatePath } from "next/cache";
import { NextRequest, NextResponse } from "next/server";

const KEY = "LSDKLSDK3o32olfsfls304j34FK";

export const GET = async (request: NextRequest) => {
  // if the KEY matches then revalidate the path

  if (request.nextUrl.searchParams.get("key") === KEY) {
    const path = request.nextUrl.searchParams.get("path");
    revalidatePath(path || "/");
    console.log("Revalidating", path || "/");
    return NextResponse.json({ revalidated: true });
  } else {
    return NextResponse.json({ message: "Not Allowed" });
  }
};
