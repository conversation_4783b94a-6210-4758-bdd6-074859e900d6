import Content from "./content";
import { Game, Response } from "@/types/games";

export const revalidate = 60 * 5;

export default async function Page() {
  const fetchData = async (): Promise<Response> => {
    try {
      const res = await fetch(`/api/games`);
      const data = await res.json();
      return data;
    } catch (error) {
      console.log(error);
      return null;
    }
  };

  const res = await fetchData();

  return (
    <div className="container mx-auto p-4 md:px-8">
      <div className="text-2xl font-semibold pb-4">Games</div>
      <Content games={res?.docs} />
    </div>
  );
}
