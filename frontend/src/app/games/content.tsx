import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import Image from "next/image";
import { Game } from "@/types/games";
import Link from "next/link";

export default function Content({ games }: { games: Game[] | undefined }) {
  return (
    <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 md:grid-cols-3">
      {games?.map((game) => (
        <Link key={game.id} href={`/games/${game.slug}`}>
          <Card className="overflow-hidden group relative">
            <Image
              className="transition-all ease-in-out duration-300 group-hover:scale-110"
              src={`${game.coverImage.url}`}
              height={900}
              width={1600}
              alt={game.title}
            />
            <CardContent className="p-4 top-[118%] transition-all ease-in-out duration-500 group-hover:top-1/2 absolute text-center h-fit bg-black/70 left-0 right-0 -translate-y-1/2">
              <div className="text-lg font-semibold text-primary">
                {game.title}
              </div>
              <div className="text-sm text-white/80">{game.tagline}</div>
            </CardContent>
          </Card>
        </Link>
      ))}
    </div>
  );
}
