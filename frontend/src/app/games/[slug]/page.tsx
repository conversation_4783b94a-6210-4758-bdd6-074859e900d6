export const revalidate = 60 * 5;

import serialize from "@/components/site/render-richtext";
import Title from "@/components/site/title";
import { Badge } from "@/components/ui/badge";
import { Card, CardHeader, CardTitle } from "@/components/ui/card";
import { Game, GameLink } from "@/types/games";
import { BoxIcon, CpuIcon } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { notFound } from "next/navigation";

type IconProps = {
  platform: GameLink["platform"];
} & Partial<React.ComponentPropsWithoutRef<"img">>;

function PlatformIcon(props: IconProps) {
  switch (props.platform) {
    case "App Store":
      return (
        <Image
          className={props.className}
          src={"/get-it-from/app-store.svg"}
          alt={props.alt as string}
          width={props.width as number}
          height={props.height as number}
        />
      );
    case "Epic":
      return (
        <Image
          className={props.className}
          src={"/get-it-from/epic-games.svg"}
          alt={props.alt as string}
          width={props.width as number}
          height={props.height as number}
        />
      );
    case "Play Store":
      return (
        <Image
          className={props.className}
          src={"/get-it-from/google-play.svg"}
          alt={props.alt as string}
          width={props.width as number}
          height={props.height as number}
        />
      );
    case "Steam":
      return (
        <Image
          className={props.className}
          src={"/get-it-from/steam.svg"}
          alt={props.alt as string}
          width={props.width as number}
          height={props.height as number}
        />
      );
    default:
      return null;
  }
}

export default async function Page({ params }: { params: { slug: string } }) {
  const fetchData = async (slug: string): Promise<Game | null> => {
    try {
      const res = await fetch(
        `/api/games/slug/${slug}`
      );
      const data = await res.json();
      return data;
    } catch (error) {
      console.log(error);
      return null;
    }
  };

  const data = await fetchData(params.slug);

  if (!data || !data?.title) return notFound();

  return (
    <div className="bg-white">
      <div className="container mx-auto p-4 md:px-8">
        <Title className="font-bold mt-4 mb-2">{data.title}</Title>
        <div className="font-semibold text-center text-xl sm:text-2xl text-primary mt-2 mb-6">
          {data.tagline}
        </div>
        <hr className="my-4" />
        <div className="container mx-auto">
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
            <Image
              src={`${data.coverImage.url}`}
              alt={data.coverImage.alt}
              width={data.coverImage.width}
              height={data.coverImage.height}
              className="w-full h-full object-cover aspect-video col-span-2 rounded-lg"
            />
            <div className="flex flex-col justify-center gap-8">
              <div className="text-2xl font-extrabold uppercase text-center">
                About the Game
              </div>
              <div className="grid grid-cols-1 place-content-center gap-2">
                {[
                  {
                    name: "GENRE",
                    values: data.genres,
                  },
                  {
                    name: "ART STYLE",
                    values: data.artstyles,
                  },
                  {
                    name: "PLATFORMS",
                    values: data.platforms,
                  },
                ].map((item, idx) => (
                  <Card key={idx}>
                    <CardHeader className="p-3">
                      <CardTitle className="flex items-center gap-2 text-primary">
                        <CpuIcon size={32} /> {item.name}
                      </CardTitle>
                      <div className="text-md font-normal text-black/70">
                        {item.values}
                      </div>
                    </CardHeader>
                  </Card>
                ))}
              </div>
            </div>
          </div>
        </div>
        <hr className="my-8" />
        <div className="bg-[#efefef] mb-4 rounded-md">
          <article className="w-full sm:w-[70%] p-4 mx-auto">
            {serialize(data.content || "")}
          </article>
        </div>
        <hr className="my-8" />
        <div className="mb-4">
          <div className="text-center text-3xl font-bold mb-4">Get it from</div>
          <div className="flex justify-center items-center gap-4 py-4">
            {data.links.map((item, idx) => (
              <Link
                key={idx}
                href={item.link}
                className="flex items-center justify-center w-16 h-16 transition-transform hover:scale-110"
              >
                <PlatformIcon
                  platform={item.platform}
                  alt={item.platform as string}
                  width={48}
                  height={48}
                  className="w-12 h-12"
                />
              </Link>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
