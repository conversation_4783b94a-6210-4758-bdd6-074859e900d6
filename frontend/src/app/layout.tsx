import type { <PERSON>ada<PERSON> } from "next";
import { Poppins as FontSans } from "next/font/google";
import "./globals.css";

import { cn } from "@/lib/utils";

import Navbar from "@/components/site/navbar";
import Footer from "@/components/site/footer";
import { Toaster } from "@/components/ui/sonner";
import SupportFloatingBtn from "@/components/site/support-floating-btn";

const fontSans = FontSans({
  subsets: ["latin"],
  variable: "--font-sans",
  weight: ["100", "200", "300", "400", "500", "600", "700", "800", "900"],
});

export const metadata: Metadata = {
  title: "Henmo<PERSON>",
  description: "Game Development Agency",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={cn(
          "min-h-screen font-sans antialiased bg-[#F9FCFF]",
          fontSans.variable
        )}
      >
        <div className="flex min-h-screen w-full flex-col">
          <Navbar />
          <main className="border-t flex min-h-[calc(100vh_-_theme(spacing.16))] flex-1 flex-col gap-4 bg-muted/40">
            {children}
          </main>
          <Footer />
        </div>
        <Toaster richColors />
        <SupportFloatingBtn />
      </body>
    </html>
  );
}
