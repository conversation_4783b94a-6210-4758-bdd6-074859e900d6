import BlogsSection from "@/components/site/blogs-sections";
import Carousel from "@/components/site/carousel";
import LatestGames from "@/components/site/latest-games";
import ServicesSection from "@/components/site/services-section";
import { WhatWeDo } from "@/components/site/what-we-do";
import { Response as SlidesResponse, Slide } from "@/types/home";
import { Response as GamesResponse, Game } from "@/types/games";

export default async function Home() {
  // Mock data for carousel slides showcasing featured games
  const slidesRes: {
    docs: Slide[]
  } = {
    docs: [
      {
        bgImage: {
          id: "slide_1_bg",
          filename: "neon-nexus-bg.jpg",
          mimeType: "image/jpeg",
          filesize: 2048000,
          width: 1920,
          height: 1080,
          focalX: 50,
          focalY: 30,
          createdAt: "2024-01-15T10:00:00Z",
          updatedAt: "2024-01-15T10:00:00Z",
          alt: "Neon Nexus cyberpunk cityscape background",
          url: "https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=1920&h=1080&fit=crop"
        },
        btnLink: "/games/neon-nexus",
        btnImage: {
          id: "btn_1",
          alt: "Play Now Button",
          filename: "play-now-btn.png",
          mimeType: "image/png",
          filesize: 15000,
          width: 200,
          height: 60,
          focalX: 50,
          focalY: 50,
          createdAt: "2024-01-15T10:00:00Z",
          updatedAt: "2024-01-15T10:00:00Z",
          url: "https://via.placeholder.com/200x60/FF6B35/FFFFFF?text=PLAY+NOW"
        },
        titleImage: {
          id: "title_1",
          alt: "Neon Nexus Game Logo",
          filename: "neon-nexus-logo.png",
          mimeType: "image/png",
          filesize: 45000,
          width: 600,
          height: 200,
          focalX: 50,
          focalY: 50,
          createdAt: "2024-01-15T10:00:00Z",
          updatedAt: "2024-01-15T10:00:00Z",
          url: "https://via.placeholder.com/600x200/00D4FF/FFFFFF?text=NEON+NEXUS"
        },
        createdAt: "2024-01-15T10:00:00Z",
        updatedAt: "2024-01-15T10:00:00Z",
        id: "slide_1",
        desc: "Dive into a cyberpunk world where technology meets humanity. Experience the ultimate sci-fi adventure."
      },
      {
        bgImage: {
          id: "slide_2_bg",
          filename: "mystic-realms-bg.jpg",
          mimeType: "image/jpeg",
          filesize: 1856000,
          width: 1920,
          height: 1080,
          focalX: 60,
          focalY: 40,
          createdAt: "2024-01-20T14:30:00Z",
          updatedAt: "2024-01-20T14:30:00Z",
          alt: "Mystic Realms fantasy landscape background",
          url: "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=1920&h=1080&fit=crop"
        },
        btnLink: "/games/mystic-realms",
        btnImage: {
          id: "btn_2",
          alt: "Explore Now Button",
          filename: "explore-btn.png",
          mimeType: "image/png",
          filesize: 16000,
          width: 220,
          height: 60,
          focalX: 50,
          focalY: 50,
          createdAt: "2024-01-20T14:30:00Z",
          updatedAt: "2024-01-20T14:30:00Z",
          url: "https://via.placeholder.com/220x60/8B5A3C/FFFFFF?text=EXPLORE+NOW"
        },
        titleImage: {
          id: "title_2",
          alt: "Mystic Realms Game Logo",
          filename: "mystic-realms-logo.png",
          mimeType: "image/png",
          filesize: 52000,
          width: 650,
          height: 180,
          focalX: 50,
          focalY: 50,
          createdAt: "2024-01-20T14:30:00Z",
          updatedAt: "2024-01-20T14:30:00Z",
          url: "https://via.placeholder.com/650x180/9D4EDD/FFFFFF?text=MYSTIC+REALMS"
        },
        createdAt: "2024-01-20T14:30:00Z",
        updatedAt: "2024-01-20T14:30:00Z",
        id: "slide_2",
        desc: "Embark on an epic fantasy journey through magical realms filled with ancient mysteries and powerful artifacts."
      },
      {
        bgImage: {
          id: "slide_3_bg",
          filename: "stellar-conquest-bg.jpg",
          mimeType: "image/jpeg",
          filesize: 2205000,
          width: 1920,
          height: 1080,
          focalX: 40,
          focalY: 35,
          createdAt: "2024-02-01T09:15:00Z",
          updatedAt: "2024-02-01T09:15:00Z",
          alt: "Stellar Conquest space battle background",
          url: "https://images.unsplash.com/photo-1446776877081-d282a0f896c7?w=1920&h=1080&fit=crop"
        },
        btnLink: "/games/stellar-conquest",
        btnImage: {
          id: "btn_3",
          alt: "Command Fleet Button",
          filename: "command-btn.png",
          mimeType: "image/png",
          filesize: 18000,
          width: 240,
          height: 60,
          focalX: 50,
          focalY: 50,
          createdAt: "2024-02-01T09:15:00Z",
          updatedAt: "2024-02-01T09:15:00Z",
          url: "https://via.placeholder.com/240x60/0077BE/FFFFFF?text=COMMAND+FLEET"
        },
        titleImage: {
          id: "title_3",
          alt: "Stellar Conquest Game Logo",
          filename: "stellar-conquest-logo.png",
          mimeType: "image/png",
          filesize: 48000,
          width: 700,
          height: 160,
          focalX: 50,
          focalY: 50,
          createdAt: "2024-02-01T09:15:00Z",
          updatedAt: "2024-02-01T09:15:00Z",
          url: "https://via.placeholder.com/700x160/FFD60A/000000?text=STELLAR+CONQUEST"
        },
        createdAt: "2024-02-01T09:15:00Z",
        updatedAt: "2024-02-01T09:15:00Z",
        id: "slide_3",
        desc: "Command massive space fleets in epic galactic warfare. Strategy meets action in the depths of space."
      }
    ]
  };

  // Mock data for latest games
  const LatestGamesRes: {
    docs: Game[]
  } = {
    docs: [
      {
        id: "game_1",
        title: "Neon Nexus",
        tagline: "The Future is Now",
        content: undefined,
        links: [
          { platform: "Steam", link: "https://store.steampowered.com/neon-nexus" },
          { platform: "Epic", link: "https://epicgames.com/neon-nexus" }
        ],
        platforms: "PC, PlayStation 5, Xbox Series X/S",
        artstyles: "Cyberpunk, Neon-noir, High-tech",
        genres: "Action RPG, Open World, Sci-Fi",
        coverImage: {
          id: "cover_1",
          alt: "Neon Nexus game cover art",
          filename: "neon-nexus-cover.jpg",
          mimeType: "image/jpeg",
          filesize: 850000,
          width: 600,
          height: 800,
          focalX: 50,
          focalY: 30,
          createdAt: "2024-01-15T10:00:00Z",
          updatedAt: "2024-01-15T10:00:00Z",
          url: "https://images.unsplash.com/photo-1551808525-51a94da548ce?w=600&h=800&fit=crop"
        },
        slug: "neon-nexus",
        createdAt: "2024-01-15T10:00:00Z",
        updatedAt: "2024-01-15T10:00:00Z"
      },
      {
        id: "game_2",
        title: "Mystic Realms",
        tagline: "Magic Awaits Beyond",
        content: undefined,
        links: [
          { platform: "Steam", link: "https://store.steampowered.com/mystic-realms" },
          { platform: "Epic", link: "https://epicgames.com/neon-nexus" }
        ],
        platforms: "PC, Nintendo Switch, PlayStation 4/5",
        artstyles: "Fantasy, Hand-painted, Whimsical",
        genres: "Adventure, Fantasy RPG, Puzzle",
        coverImage: {
          id: "cover_2",
          alt: "Mystic Realms game cover art",
          filename: "mystic-realms-cover.jpg",
          mimeType: "image/jpeg",
          filesize: 920000,
          width: 600,
          height: 800,
          focalX: 50,
          focalY: 40,
          createdAt: "2024-01-20T14:30:00Z",
          updatedAt: "2024-01-20T14:30:00Z",
          url: "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=600&h=800&fit=crop"
        },
        slug: "mystic-realms",
        createdAt: "2024-01-20T14:30:00Z",
        updatedAt: "2024-01-20T14:30:00Z"
      },
      {
        id: "game_3",
        title: "Stellar Conquest",
        tagline: "Conquer the Galaxy",
        content: undefined,
        links: [
          { platform: "Steam", link: "https://store.steampowered.com/stellar-conquest" },
          { platform: "Epic", link: "https://epicgames.com/neon-nexus" }
        ],
        platforms: "PC, Xbox Series X/S, PlayStation 5",
        artstyles: "Realistic, Space Opera, Military Sci-Fi",
        genres: "Strategy, Real-time Strategy, Space Simulation",
        coverImage: {
          id: "cover_3",
          alt: "Stellar Conquest game cover art",
          filename: "stellar-conquest-cover.jpg",
          mimeType: "image/jpeg",
          filesize: 780000,
          width: 600,
          height: 800,
          focalX: 50,
          focalY: 25,
          createdAt: "2024-02-01T09:15:00Z",
          updatedAt: "2024-02-01T09:15:00Z",
          url: "https://images.unsplash.com/photo-1446776877081-d282a0f896c7?w=600&h=800&fit=crop"
        },
        slug: "stellar-conquest",
        createdAt: "2024-02-01T09:15:00Z",
        updatedAt: "2024-02-01T09:15:00Z"
      },
      {
        id: "game_4",
        title: "Shadow Hunter Chronicles",
        tagline: "Hunt in the Darkness",
        content: undefined,
        links: [
          { platform: "Steam", link: "https://store.steampowered.com/shadow-hunter" },
          { platform: "Epic", link: "https://epicgames.com/neon-nexus" }
        ],
        platforms: "PC, PlayStation 4/5, Xbox One/Series X/S",
        artstyles: "Dark Fantasy, Gothic, Atmospheric",
        genres: "Action, Horror, Third-person Shooter",
        coverImage: {
          id: "cover_4",
          alt: "Shadow Hunter Chronicles game cover art",
          filename: "shadow-hunter-cover.jpg",
          mimeType: "image/jpeg",
          filesize: 695000,
          width: 600,
          height: 800,
          focalX: 50,
          focalY: 35,
          createdAt: "2024-02-10T16:45:00Z",
          updatedAt: "2024-02-10T16:45:00Z",
          url: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=600&h=800&fit=crop"
        },
        slug: "shadow-hunter-chronicles",
        createdAt: "2024-02-10T16:45:00Z",
        updatedAt: "2024-02-10T16:45:00Z"
      },
      {
        id: "game_5",
        title: "Pixel Paradise",
        tagline: "Retro Gaming Reimagined",
        content: undefined,
        links: [
          { platform: "Steam", link: "https://store.steampowered.com/pixel-paradise" },
          { platform: "Epic", link: "https://epicgames.com/neon-nexus" }
        ],
        platforms: "PC, Nintendo Switch, iOS, Android",
        artstyles: "Pixel Art, Retro, 16-bit Inspired",
        genres: "Platformer, Indie, Casual",
        coverImage: {
          id: "cover_5",
          alt: "Pixel Paradise game cover art",
          filename: "pixel-paradise-cover.jpg",
          mimeType: "image/jpeg",
          filesize: 450000,
          width: 600,
          height: 800,
          focalX: 50,
          focalY: 45,
          createdAt: "2024-02-15T11:20:00Z",
          updatedAt: "2024-02-15T11:20:00Z",
          url: "https://images.unsplash.com/photo-1493711662062-fa541adb3fc8?w=600&h=800&fit=crop"
        },
        slug: "pixel-paradise",
        createdAt: "2024-02-15T11:20:00Z",
        updatedAt: "2024-02-15T11:20:00Z"
      }
    ]
  };

  if (!LatestGamesRes || !slidesRes) {
    return null;
  }

  return (
    <div className="bg-[#F9FCFF] mt-[-1px]">
      <Carousel slides={slidesRes.docs} />
      <WhatWeDo />
      <LatestGames games={LatestGamesRes.docs} />
      <ServicesSection />
      <BlogsSection />
    </div>
  );
}
