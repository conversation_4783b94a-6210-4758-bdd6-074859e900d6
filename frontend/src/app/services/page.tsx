import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import Image from "next/image";
import WorkTogetherForm from "./form";

export default function Page() {
  return (
    <div className="w-full mt-[-1px] space-y-6 mb-6 bg-secondary">
      <div className="py-8 md:px-8 space-y-2 bg-primary text-white">
        <div className="w-[94%] sm:container mx-auto space-y-8">
          <div className="">
            <h2 className="text-2xl text-center font-semibold">
              WHY HENMOVA FOR
            </h2>
            <h1 className="sm:text-4xl text-3xl text-center font-extrabold">
              GAME DEVELOPMENT
            </h1>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {[1, 2, 3].map((item, idx) => (
              <Card
                key={idx}
                className="overflow-hidden rounded-2xl border-none shadow-md shadow-black/40"
              >
                <CardHeader className="text-white bg-[#3F3F3F] flex flex-row items-center gap-2">
                  <Image
                    src={"/unity 1.png"}
                    width={50}
                    height={50}
                    alt={"img"}
                  />
                  <CardTitle className="text-center font-bold">
                    UNITY GAME DEVELOPMENT
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-4">
                  Experience the magic of Unity game development in both 2D and
                  3D dimensions! Our expert team brings your ideas to life with
                  precision and creativity, crafting immersive experiences that
                  captivate players. Whether you&apos;re diving into classic 2D
                  adventures or exploring dynamic 3D worlds, we&apos;re here to
                  turn your vision into reality with seamless gameplay and
                  stunning visuals.
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
      <div className="w-[94%] sm:container mx-auto space-y-8">
        <div className="text-primary text-center">
          <h5 className="text-xl font-bold">LET&apos;S</h5>
          <h1 className="text-3xl font-extrabold">WORK TOGETHER</h1>
        </div>
        <div className="max-w-lg mx-auto bg-[#3F3F3F] text-white p-6 rounded-md">
          <WorkTogetherForm />
        </div>
      </div>
    </div>
  );
}
